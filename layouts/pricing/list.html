{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium pricing-page">
  <div class="container">
    <div class="columns">
      <div class="column is-centered-tablet  has-text-centered">
        <h1 class="title section-title">{{ .Title }}</h1>
        <h5 class="subtitle is-5 is-muted">{{ .Params.Subtitle }}</h5>
        <div class="divider is-centered"></div>
      </div>
    </div>

    <div class="content">
      <div class="columns">
        <div class="column is-8 is-offset-2">
          <div class="content">
            <p>We believe cloud pricing should be as flexible as the infrastructure itself.</p>
            <p>Rather than fixed plans, we offer custom quotes based on your specific needs — whether you're running a few virtual machines or building a sovereign, multi-tenant cloud.</p>
            <p>Tell us how much capacity you need below, and we'll provide a tailored proposal.</p>
          </div>

          <div class="quote-form">
            <!-- Success Message (initially hidden) -->
            <div
              id="form-success"
              class="form-message success"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-success').style.display='none'"
              >&times;</button>
              <strong>Thank you!</strong> Your quote request has been submitted successfully.
              We'll get back to you within 1 business day.
            </div>

            <!-- Error Message (initially hidden) -->
            <div
              id="form-error"
              class="form-message error"
            >
              <button
                class="close-btn"
                onclick="document.getElementById('form-error').style.display='none'"
              >&times;</button>
              There was a problem submitting your quote request. Please try again.
            </div>

            <h3 class="form-title">Quote Request Form</h3>

            <form
              id="pricing-form"
              action="{{ .Site.Params.section5.action }}"
              method="POST"
            >
              <div class="columns is-multiline">
                <!-- Name and Email Row -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Your Name</label>
                    <input
                      class="input"
                      name="name"
                      type="text"
                      placeholder="Enter your name"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Email Address</label>
                    <input
                      class="input"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>
                </div>
                <!-- Phone and Company Row -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Phone Number</label>
                    <input
                      class="input"
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="Enter your phone number"
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Company Name</label>
                    <input
                      class="input"
                      id="company"
                      name="company"
                      type="text"
                      placeholder="Enter your company name"
                    />
                  </div>
                </div>
                <!-- Technical Requirements -->
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Number of vCPUs</label>
                    <input
                      class="input"
                      id="vcpus"
                      name="vcpus"
                      type="number"
                      min="1"
                      placeholder="e.g., 16"
                      required
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Memory (in GB)</label>
                    <input
                      class="input"
                      id="memory"
                      name="memory"
                      type="number"
                      min="1"
                      placeholder="e.g., 64"
                      required
                    />
                  </div>
                </div>

                <div class="column is-6">
                  <div class="field">
                    <label class="label">Block Storage (in TB)</label>
                    <input
                      class="input"
                      id="blockStorage"
                      name="blockStorage"
                      type="number"
                      min="0"
                      placeholder="e.g., 2"
                    />
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Object Storage (in TB)</label>
                    <input
                      class="input"
                      id="objectStorage"
                      name="objectStorage"
                      type="number"
                      min="0"
                      placeholder="e.g., 10"
                    />
                  </div>
                </div>

                <!-- Service Model -->
                <div class="column is-12">
                  <div class="field">
                    <label class="label">Service Model</label>
                    <div class="radio-group" style="display: flex;">
                      <div class="radio-option">
                        <input type="radio" id="self-managed" name="serviceModel" value="self-managed" required>
                        <label for="self-managed">Self Managed Cloud</label>
                      </div>
                      <div class="radio-option" style="margin-left: 15px;">
                        <input type="radio" id="caas" name="serviceModel" value="caas" required>
                        <label for="caas">Cloud as a Service</label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Hardware Quote Checkbox -->
                <div class="column is-12">
                  <div class="checkbox-field">
                    <input type="checkbox" id="hardwareQuote" name="hardwareQuote">
                    <label for="hardwareQuote">I would like to receive a hardware quote as well</label>
                  </div>
                  <p class="form-note">* Hardware quotes are delivered by a trusted partner of whitesky.cloud, not by whitesky.cloud itself.</p>
                </div>

                <!-- Additional Information -->
                <div class="column is-12">
                  <div class="field">
                    <label class="label">Additional Requests or Information</label>
                    <textarea
                      class="textarea"
                      id="additionalInfo"
                      name="additionalInfo"
                      rows="4"
                      placeholder="e.g., Need GPU support (NVIDIA A100), or Database as a Service..."
                    ></textarea>
                  </div>
                </div>

                <!-- Hidden fields for backend -->
                <input type="hidden" name="subject" value="Quote Request" />
                <textarea name="message" style="display: none;" readonly></textarea>
                <!-- honeypot field to prevent spam -->
                <input type="text" name="_gotcha" style="display: none" />
                <!-- bot detection data for server-side validation -->
                <input type="hidden" name="_bot_detection_data" id="bot-detection-data" />

                <div class="column is-12">
                  <button
                    id="submit-button"
                    class="submit-button button cta is-large primary-btn raised is-clear"
                    type="submit"
                  >
                    Submit Quote Request
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- How It Works Section -->
          <div class="info-section">
            <h2>How It Works</h2>
            <ul>
              <li>You fill in your capacity needs</li>
              <li>We calculate a tailored, no-obligation quote</li>
              <li>We'll reach out personally within 1 business day</li>
            </ul>

            <h3>Why We Do It This Way</h3>
            <p>Our platform supports private, public, and hybrid cloud models — and we want to make sure you get the best setup at the right price. By understanding your requirements, we can recommend the ideal combination of performance, resilience, and cost.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  var form, submitButton, successMessage, errorMessage

  // Bot detection variables - collect data for server-side validation
  var botDetection = {
    pageLoadTime: Date.now(),
    hasMouseMovement: false,
    hasFieldFocus: false,
    minSubmissionTime: 2000, // 2 seconds minimum
    mouseMoveThreshold: 5, // minimum mouse movements
    mouseMoveCount: 0,
    fieldFocusEvents: [],
    keyboardEvents: 0
  };

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("pricing-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");

    // Initialize bot detection
    initBotDetection();

    // Add form submit event listener
    form.addEventListener("submit", function(e) {
      e.preventDefault();
      submitForm();
    });
  });

  function initBotDetection() {
    // Track mouse movement
    document.addEventListener("mousemove", function() {
      botDetection.mouseMoveCount++;
      if (botDetection.mouseMoveCount >= botDetection.mouseMoveThreshold) {
        botDetection.hasMouseMovement = true;
      }
    });

    // Track field focus
    var formFields = form.querySelectorAll('input, textarea, select');
    formFields.forEach(function(field) {
      field.addEventListener("focus", function() {
        botDetection.hasFieldFocus = true;
        botDetection.fieldFocusEvents.push({
          field: field.name || field.id || 'unknown',
          timestamp: Date.now()
        });
      });

      // Track keyboard events
      field.addEventListener("keydown", function() {
        botDetection.keyboardEvents++;
      });
    });
  }

  function collectBotDetectionData() {
    var submissionTime = Date.now() - botDetection.pageLoadTime;

    // Check honeypot field
    var honeypot = document.querySelector('input[name="_gotcha"]');
    var honeypotFilled = honeypot && honeypot.value.trim() !== '';

    // Collect all bot detection data for server-side validation
    var detectionData = {
      submissionTime: submissionTime,
      hasMouseMovement: botDetection.hasMouseMovement,
      mouseMoveCount: botDetection.mouseMoveCount,
      hasFieldFocus: botDetection.hasFieldFocus,
      fieldFocusEvents: botDetection.fieldFocusEvents.length,
      keyboardEvents: botDetection.keyboardEvents,
      honeypotFilled: honeypotFilled,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    };

    return JSON.stringify(detectionData);
  }

  function buildMessageFromFormData() {
    // Collect all form data except name, email, subject, message, and hidden fields
    var phone = document.getElementById("phone").value;
    var company = document.getElementById("company").value;
    var vcpus = document.getElementById("vcpus").value;
    var memory = document.getElementById("memory").value;
    var blockStorage = document.getElementById("blockStorage").value;
    var objectStorage = document.getElementById("objectStorage").value;
    var serviceModel = document.querySelector('input[name="serviceModel"]:checked');
    var hardwareQuote = document.getElementById("hardwareQuote").checked;
    var additionalInfo = document.getElementById("additionalInfo").value;

    var message = "QUOTE REQUEST DETAILS:\n\n";

    // Contact Information
    message += "Contact Information:\n";
    message += "- Phone: " + (phone || "Not provided") + "\n";
    message += "- Company: " + (company || "Not provided") + "\n\n";

    message += "Technical Requirements:\n";
    message += "- vCPUs: " + (vcpus || "Not specified") + "\n";
    message += "- Memory: " + (memory || "Not specified") + " GB\n";
    message += "- Block Storage: " + (blockStorage || "0") + " TB\n";
    message += "- Object Storage: " + (objectStorage || "0") + " TB\n";
    message += "- Service Model: " + (serviceModel ? serviceModel.value : "Not specified") + "\n";
    message += "- Hardware Quote Requested: " + (hardwareQuote ? "Yes" : "No") + "\n";

    if (additionalInfo) {
      message += "\nAdditional Information:\n" + additionalInfo;
    }

    return message;
  }

  async function submitForm() {
    // Validate form before submitting
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    // Hide any previous messages
    successMessage.style.display = "none";
    errorMessage.style.display = "none";

    // Change button text to show loading state
    var originalButtonText = submitButton.textContent;
    submitButton.textContent = "Sending...";
    submitButton.disabled = true;

    // Build the message from form data
    var messageField = document.querySelector('textarea[name="message"]');
    messageField.value = buildMessageFromFormData();

    // Add bot detection data to hidden field for server-side validation
    var botDetectionField = document.getElementById('bot-detection-data');
    botDetectionField.value = collectBotDetectionData();

    // Create form data from the form element
    var data = new FormData(form);

    try {
      const response = await fetch(form.action, {
        method: form.method,
        body: data,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        successMessage.style.display = "block";
        form.reset();
        // Scroll to success message
        successMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      } else {
        errorMessage.style.display = "block";
        errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    } catch (error) {
      console.error("Form submission error:", error);
      errorMessage.style.display = "block";
      errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    submitButton.textContent = originalButtonText;
    submitButton.disabled = false;
  }

</script>

{{ partial "footer.html" . }}

{{ end }}
