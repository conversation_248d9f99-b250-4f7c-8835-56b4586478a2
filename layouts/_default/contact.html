{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium">
  <div class="container">
    <div class="columns">
      <div class="column is-centered-tablet-portrait">
        <h1 class="title section-title">{{ .Title }}</h1>
        <h5 class="subtitle is-5 is-muted">{{ .Params.description }}</h5>
        <div class="divider"></div>
      </div>
    </div>

    <div class="content">
      {{ .Content }}
    </div>
  </div>
</section>

<!-- Contact Form Section -->
<section class="section section-light-grey is-medium contact-form-section" id="contact-form">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2 is-spaced">Send us a Message</h2>
      <h3 class="subtitle is-5 is-muted">We'd love to hear from you</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        <div class="column is-6 is-offset-3">
          <div class="form-wrapper">
            <!-- Success Message (initially hidden) -->
            <div
              id="form-success"
              class="notification is-success"
              style="display: none"
            >
              <button
                class="delete"
                onclick="document.getElementById('form-success').style.display='none'"
              ></button>
              <strong>Thank you!</strong> Your message has been sent successfully.
              We'll get back to you soon.
            </div>

            <!-- Error Message (initially hidden) -->
            <div
              id="form-error"
              class="notification is-danger"
              style="display: none"
            >
              <button
                class="delete"
                onclick="document.getElementById('form-error').style.display='none'"
              ></button>
              There was a problem submitting your form. Please try again.
            </div>

            <form
              id="contact-form"
              action="{{ .Site.Params.section5.action }}"
              method="{{ .Site.Params.section5.method }}"
            >
              <div class="columns is-multiline">
                <div class="column is-6">
                  <input
                    class="input is-medium"
                    name="name"
                    type="text"
                    placeholder="Enter your name"
                    required
                  />
                </div>
                <div class="column is-6">
                  <input
                    class="input is-medium"
                    name="email"
                    type="email"
                    placeholder="Enter your email address"
                    required
                  />
                </div>
                <div class="column is-12">
                  <input
                    class="input is-medium"
                    name="subject"
                    type="text"
                    placeholder="Subject"
                  />
                </div>
                <div class="column is-12">
                  <textarea
                    class="textarea"
                    name="message"
                    rows="10"
                    placeholder="Write something..."
                    required
                  ></textarea>
                </div>
                <!-- Formspree honeypot field to prevent spam -->
                <input type="text" name="_gotcha" style="display: none" />
                <!-- bot detection data for server-side validation -->
                <input type="hidden" name="_bot_detection_data" id="contact-bot-detection-data" />
                <div class="form-footer has-text-centered mt-10">
                  <button
                    id="submit-button"
                    class="button cta is-large primary-btn raised is-clear"
                    type="submit"
                  >
                    Send Message
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>



<script>
  var contactForm, contactSubmitButton, contactSuccessMessage, contactErrorMessage

  // Bot detection variables for contact form - collect data for server-side validation
  var contactBotDetection = {
    pageLoadTime: Date.now(),
    hasMouseMovement: false,
    hasFieldFocus: false,
    minSubmissionTime: 2000, // 2 seconds minimum
    mouseMoveThreshold: 5, // minimum mouse movements
    mouseMoveCount: 0,
    fieldFocusEvents: [],
    keyboardEvents: 0
  };

  document.addEventListener("DOMContentLoaded", function () {
    contactForm = document.getElementById("contact-form");
    contactSubmitButton = document.getElementById("submit-button");
    contactSuccessMessage = document.getElementById("form-success");
    contactErrorMessage = document.getElementById("form-error");

    // Initialize bot detection for contact form
    initContactBotDetection();

    // Add form submit event listener
    contactForm.addEventListener("submit", function(e) {
      e.preventDefault();
      submitContactForm();
    });
  });

  function initContactBotDetection() {
    // Track mouse movement
    document.addEventListener("mousemove", function() {
      contactBotDetection.mouseMoveCount++;
      if (contactBotDetection.mouseMoveCount >= contactBotDetection.mouseMoveThreshold) {
        contactBotDetection.hasMouseMovement = true;
      }
    });

    // Track field focus for contact form
    var formFields = contactForm.querySelectorAll('input, textarea, select');
    formFields.forEach(function(field) {
      field.addEventListener("focus", function() {
        contactBotDetection.hasFieldFocus = true;
        contactBotDetection.fieldFocusEvents.push({
          field: field.name || field.id || 'unknown',
          timestamp: Date.now()
        });
      });

      // Track keyboard events
      field.addEventListener("keydown", function() {
        contactBotDetection.keyboardEvents++;
      });
    });
  }

  function collectContactBotDetectionData() {
    var submissionTime = Date.now() - contactBotDetection.pageLoadTime;

    // Check honeypot field
    var honeypot = contactForm.querySelector('input[name="_gotcha"]');
    var honeypotFilled = honeypot && honeypot.value.trim() !== '';

    // Collect all bot detection data for server-side validation
    var detectionData = {
      submissionTime: submissionTime,
      hasMouseMovement: contactBotDetection.hasMouseMovement,
      mouseMoveCount: contactBotDetection.mouseMoveCount,
      hasFieldFocus: contactBotDetection.hasFieldFocus,
      fieldFocusEvents: contactBotDetection.fieldFocusEvents.length,
      keyboardEvents: contactBotDetection.keyboardEvents,
      honeypotFilled: honeypotFilled,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    };

    return JSON.stringify(detectionData);
  }



  async function submitContactForm() {
    // Validate form before submitting
    if (!contactForm.checkValidity()) {
      contactForm.reportValidity();
      return;
    }

    // Hide any previous messages
    contactSuccessMessage.style.display = "none";
    contactErrorMessage.style.display = "none";

    // Change button text to show loading state
    var originalButtonText = contactSubmitButton.textContent;
    contactSubmitButton.textContent = "Sending...";
    contactSubmitButton.disabled = true;

    // Add bot detection data to hidden field for server-side validation
    var botDetectionField = document.getElementById('contact-bot-detection-data');
    botDetectionField.value = collectContactBotDetectionData();

    // Create form data from the form element
    var data = new FormData(contactForm);

    try {
      const response = await fetch(contactForm.action, {
        method: contactForm.method,
        body: data,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        contactSuccessMessage.style.display = "block";
        contactForm.reset();
        // Scroll to success message
        contactSuccessMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      } else {
        contactErrorMessage.style.display = "block";
        contactErrorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    } catch (error) {
      console.error("Contact form submission error:", error);
      contactErrorMessage.style.display = "block";
      contactErrorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    contactSubmitButton.textContent = originalButtonText;
    contactSubmitButton.disabled = false;
  }
</script>

{{ partial "section5.html" . }}
{{ partial "footer.html" . }}

{{ end }}
